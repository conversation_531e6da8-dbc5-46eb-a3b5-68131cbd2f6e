<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <script src='https://unpkg.com/htmx.org/dist/htmx.min.js'></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"
        integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo"
        crossorigin="anonymous"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <!-- Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    <title>Welcome</title>
    <th:block th:insert="fragments/favicon :: favicon"></th:block>
    <style>
        .hanging-indent {
            padding-left: 1.5rem;
            text-indent: -1.5rem;
        }
    </style>
    <script th:src="@{/js/utils.js}"></script>
    <script th:inline="javascript" type="module">
        document.addEventListener("DOMContentLoaded", function () {
            var isSuperAdmin = /*[[${isSuperAdmin}]]*/ "";
            var isAdmin = /*[[${isAdmin}]]*/ "";
            if (isLoggedIn() && !isProfileCompleted()) {
                document.getElementById("profile-link-ctr")?.classList?.remove("hidden");
            }
            if (isSuperAdmin || isAdmin) {
                const startInput = document.getElementById('datepicker-range-start');
                const endInput = document.getElementById('datepicker-range-end');

                const today = new Date();
                const pastDate = new Date();
                pastDate.setDate(today.getDate() - 30);

                function formatDate(date) {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                }
                document.getElementById('dateFilterButton').addEventListener('click', handleDateChange);

                flatpickr(startInput, {
                    dateFormat: "Y-m-d",
                    defaultDate: formatDate(pastDate)
                });

                flatpickr(endInput, {
                    dateFormat: "Y-m-d",
                    defaultDate: formatDate(today)
                });

                handleDateChange();

                function handleDateChange() {
                    const startDate = startInput.value;
                    const endDate = endInput.value;
                    // Validate the dates
                    if (new Date(startDate) > new Date(endDate)) {
                        document.getElementById('start-date-error').textContent = "Start date cannot be after end date.";
                        return;
                    }
                    else {
                        document.getElementById('start-date-error').textContent = "";
                        document.getElementById('end-date-error').textContent = "";
                    }
                    onDateRangeChanged(startDate, endDate);
                }

                function onDateRangeChanged(startDate, endDate) {
                    // Call your fetch function
                    getSessionDetails(startDate, endDate);
                }
                function getSessionDetails(startDate, endDate) {
                    let apiUrl = '';
                    if (new Date(startDate) > new Date(endDate)) {
                        document.getElementById('start-date-error').textContent = "Start date cannot be after end date.";
                        return;
                    }
                    else {
                        document.getElementById('start-date-error').textContent = "";
                        document.getElementById('end-date-error').textContent = "";
                    }
                    // Determine the API URL based on user role
                    if (isAdmin) {
                        apiUrl = `/sessions/organization/report?startDate=${startDate}&endDate=${endDate}`;
                    } else if (isSuperAdmin) {
                        apiUrl = `/sessions/application/report?startDate=${startDate}&endDate=${endDate}`;
                    } else {
                        return
                    }

                    fetchRawData(
                        apiUrl,
                        (res, error) => {
                            const orgData = isSuperAdmin ? res.data?.total : res.data.data[0];
                            if (orgData !== undefined && orgData !== null) {
                                const metrics = [
                                    {
                                        title: 'Organization Name', data: orgData.organization_name, svg: `<svg class="w-6 h-6 text - gray - 800 dark: text - white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.14294 20v-9l-4 1.125V20h4Zm0 0V6.66667m0 13.33333h2.99996m5-9V6.66667m0 4.33333 4 1.125V13m-4-2v3m2-6-6-4-5.99996 4m4.99996 1h2m-2 3h2m1 6 2 2 4-4" />
</svg > ` },
                                    {
                                        title: 'Registered Users', data: orgData.registered_user_count, svg: `<svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
  <path fill-rule="evenodd" d="M12 6a3.5 3.5 0 1 0 0 7 3.5 3.5 0 0 0 0-7Zm-1.5 8a4 4 0 0 0-4 4 2 2 0 0 0 2 2h7a2 2 0 0 0 2-2 4 4 0 0 0-4-4h-3Zm6.82-3.096a5.51 5.51 0 0 0-2.797-6.293 3.5 3.5 0 1 1 2.796 6.292ZM19.5 18h.5a2 2 0 0 0 2-2 4 4 0 0 0-4-4h-1.1a5.503 5.503 0 0 1-.471.762A5.998 5.998 0 0 1 19.5 18ZM4 7.5a3.5 3.5 0 0 1 5.477-2.889 5.5 5.5 0 0 0-2.796 6.293A3.501 3.501 0 0 1 4 7.5ZM7.1 12H6a4 4 0 0 0-4 4 2 2 0 0 0 2 2h.5a5.998 5.998 0 0 1 3.071-5.238A5.505 5.505 0 0 1 7.1 12Z" clip-rule="evenodd"/>
</svg>
` },
                                    {
                                        title: 'Active Users', data: orgData.currently_active_users_count, svg: `<svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
  <path fill-rule="evenodd" d="M8 4a4 4 0 1 0 0 8 4 4 0 0 0 0-8Zm-2 9a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-1a4 4 0 0 0-4-4H6Zm7.25-2.095c.478-.86.75-1.85.75-2.905a5.973 5.973 0 0 0-.75-2.906 4 4 0 1 1 0 5.811ZM15.466 20c.34-.588.535-1.271.535-2v-1a5.978 5.978 0 0 0-1.528-4H18a4 4 0 0 1 4 4v1a2 2 0 0 1-2 2h-4.535Z" clip-rule="evenodd"/>
</svg>
 ` },
                                    {
                                        title: 'Total Visit Count', data: orgData.total_visit_count, svg: `<svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
  <path fill-rule="evenodd" d="M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2h-7Z" clip-rule="evenodd"/>
</svg>
 ` },

                                ];

                                const container = document.getElementById('metrics-container');
                                container.innerHTML = ''; // Clear previous content
                                metrics.forEach(metric => {
                                    if (isSuperAdmin && metric.title === 'Organization Name') {
                                        metric.data = orgData.application_name;
                                        return;
                                    }
                                    const wrapper = document.createElement('div');
                                    wrapper.className = 'flex items-center space-x-4 mb-2'; // horizontal layout with spacing

                                    // Left: SVG icon
                                    const iconWrapper = document.createElement('div');
                                    iconWrapper.innerHTML = metric.svg;
                                    iconWrapper

                                    // Right: Text content (title and data)
                                    const textWrapper = document.createElement('div');
                                    textWrapper.className = 'flex flex-col'; // column layout for title and data

                                    const dt = document.createElement('div');
                                    dt.className = 'truncate text-sm font-semibold text-gray-500';
                                    dt.textContent = metric.title;

                                    const dd = document.createElement('div');
                                    dd.className = 'truncate mt-1 text-sm font-normal tracking-tight text-gray-600';
                                    dd.textContent = metric.data;

                                    textWrapper.appendChild(dt);
                                    textWrapper.appendChild(dd);

                                    wrapper.appendChild(iconWrapper);
                                    wrapper.appendChild(textWrapper);
                                    container.appendChild(wrapper);
                                });

                            }
                            else {
                                const container = document.getElementById('metrics-container');
                                container.innerHTML = '<div class="text-center text-gray-500">No data available for the selected date range.</div>';
                            }
                        }

                    );
                }
            }
        });
    </script>
</head>

<body class="bg-gray-100">
    <div layout:fragment="content" class="flex lg:flex-row sm:flex-col md:flex-col items-center justify-center p-4">
        <!-- <div clas="flex flex-row"> -->
        <div class="basis-4/5">
            <div class="flex justify-center">
                <img src="/diabetes-research-hub.svg" class="h-auto max-w-32" />
            </div>

            <div class="flex text-2xl sm:text-4xl font-bold py-2 w-480 justify-center">Diabetes Research Hub</div>
            <!-- <img src="/techbd-hub.svg" class="mb-8" /> -->
            <div class="flex justify-center">
                <div class="prose bg-white p-1 w-full max-w-6xl text-center sm:p-8 lg:p-8">
                    <h1 class="text-2xl sm:text-3xl font-bold mb-4 text-center">Introducing Our CGM Data Visualization
                        Tool: A
                        Cutting-Edge Solution for Diabetes Research</h1>
                    <p class="mb-4 text-justify"> Our CGM Data Visualization Tool is in active development, continuously
                        evolving
                        with innovative features and enhanced functionalities. Tailored for researchers, clinicians, and
                        individuals managing diabetes, this tool offers intuitive visualizations and robust analytics,
                        making it
                        easier than ever to explore and interpret continuous glucose monitoring (CGM) data. Whether
                        you're
                        examining glucose trends, assessing glycemic control, or measuring the effects of interventions,
                        our
                        tool delivers the insights you need to drive informed research and clinical decisions.</p>

                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                        <ul class="list-disc list-inside sm:pl-8">
                            <li class="mb-2 hanging-indent">The DRH CGM Data Visualization Tool aggregates studies from
                                multiple
                                investigators, compatible with data from any CGM device.
                                <a th:href="@{/studies/all}">Explore the comprehensive study submissions.</a>
                            </li>

                        </ul>
                        <ul class="list-disc list-inside sm:pl-8">
                            <li class="mb-2 hanging-indent">
                                The hub offers an in-depth view of individual studies and generates comprehensive
                                reports
                                featuring key metrics such as the
                                <a href="https://diatribe.org/diabetes-technology/making-most-cgm-uncover-magic-your-ambulatory-glucose-profile"
                                    target="_blank">Ambulatory
                                    Glucose Profile, Time in Range goals, Daily
                                    Glucose Profile</a>, <a href="https://www.diabetestechnology.org/gri/"
                                    target="_blank">Glycemia Risk
                                    Index</a>, and more.
                            </li>

                        </ul>

                        <ul class="list-disc list-inside sm:pl-8">

                            <li class="mb-2 hanging-indent">The Hub promotes the sharing of documentation <a
                                    href="https://drh.diabetestechnology.org/getting-started" target="_blank">Dive into
                                    our
                                    docs</a> and <a th:href="@{/docs/swagger}">Explore
                                    our APIs</a>.</li>
                        </ul>


                        <ul th:if="${isAuthenticated && !isProfileCompleted && !isSuperAdmin}"
                            class="list-disc list-inside sm:pl-8" id="profile-link-ctr">
                            <li class="mb-2 hanging-indent">You're logged in as <span th:text="${userProvider}"></span>
                                User
                                <span th:text="${userName}"></span>.
                                Create your user profile in order to upload studies
                                <a th:href="@{/userprofile/info}">
                                    <button type="submit" id="user-profile-link"
                                        class="text-white bg-[#1F883D] focus:ring-4 focus:outline-none font-bold rounded text-sm inline-flex items-center px-4 py-1.5 text-center mt-2">
                                        Complete Your DRH Profile
                                    </button>
                                </a>
                            </li>
                        </ul>

                        <ul th:if="${isAuthenticated && isProfileCompleted && !isSuperAdmin}"
                            class="list-disc list-inside sm:pl-8" id="profile-link-ctr">
                            <li class="mb-2 hanging-indent">You're logged in as <span th:text="${userProvider}"></span>
                                User
                                <span th:text="${userName}"></span>.
                                Your Institution is <span th:text="${Organization}"></span>.
                            </li>
                        </ul>
                        <ul th:if="${isAuthenticated && !isProfileCompleted && isSuperAdmin}"
                            class="list-disc list-inside sm:pl-8" id="profile-link-ctr">
                            <li class="mb-2 hanging-indent">You're logged in as <span th:text="${userProvider}"></span>
                                User
                                <span th:text="${userName}"></span>.
                                Your Institution is <span th:text="${Organization}"></span>.
                            </li>
                        </ul>
                        <ul th:if="${!isAuthenticated}" class="list-disc list-inside sm:pl-8" id="profile-link-ctr">
                            <li class="mb-2 hanging-indent">You are not logged in. You are viewing studies without visit
                                tracking.
                            </li>
                        </ul>
                    </div>
                </div>

            </div>
        </div>
        <div th:if="${isAuthenticated && (isSuperAdmin || isAdmin)}"
            class="basis-1/5 overflow-hidden rounded-lg bg-white p-4 shadow">
            <div class="justify-center pb-5">
                <!-- <div style="display: flex"> -->
                <div class="py-2">
                    <h2 class="text-2xl font-semibold text-gray-700 mb-2 flex">
                        Visitors Details
                    </h2>
                    <p class="text-left"> Select a date range to view metrics</p>
                    <div class="py-3">
                        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
                        <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

                        <div class="flex flex-col gap-2 mt-4">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center ps-3 pointer-events-none">
                                    <!-- Calendar Icon -->
                                    <svg class="w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg"
                                        fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            d="M6 2a1 1 0 1 1 2 0v1h4V2a1 1 0 1 1 2 0v1h1a2 2 0 0 1 2 2v1H3V5a2 2 0 0 1 2-2h1V2Zm11 5H3v9a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V7ZM5 10a1 1 0 1 1 0 2 1 1 0 0 1 0-2Zm4 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2Zm4 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2Z" />
                                    </svg>
                                </div>
                                <input id="datepicker-range-start" type="text" class="ps-10  border rounded"
                                    placeholder="Start Date">
                            </div>
                            <span id="start-date-error" class="text-sm font-normal text-red-500"></span>

                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center ps-3 pointer-events-none">
                                    <svg class="w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg"
                                        fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            d="M6 2a1 1 0 1 1 2 0v1h4V2a1 1 0 1 1 2 0v1h1a2 2 0 0 1 2 2v1H3V5a2 2 0 0 1 2-2h1V2Zm11 5H3v9a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V7ZM5 10a1 1 0 1 1 0 2 1 1 0 0 1 0-2Zm4 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2Zm4 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2Z" />
                                    </svg>
                                </div>
                                <input id="datepicker-range-end" type="text" class="ps-10  border rounded"
                                    placeholder="End Date">
                            </div>
                            <span id="end-date-error" class="text-sm font-normal text-red-500"></span>
                            <button type="button" id="dateFilterButton"
                                class="bg-[#1F883D] text-sm text-white px-5 p-[11px] rounded hover:bg-[#1F883D] font-bold">
                                Apply Filter
                            </button>
                        </div>


                    </div>
                </div>
                <!-- </div> -->
                <div class="w-[100%] justify-between grid md:grid-cols-1 sm:grid-cols-1 gap-4 lg:grid-row-4 border border-gray-200 bg-gray-50 rounded py-3 px-5"
                    id="metrics-container">

                </div>
            </div>
        </div>
        <!-- </div> -->

        <!-- <div sec:authorize="isAuthenticated()">
            <div>You're logged in as <span sec:authentication="principal.attributes['provider']"></span> User <span
                    sec:authentication="principal.attributes['name']"></span>.</div>
        </div> -->
        <!-- <div sec:authorize="isAnonymous()">
            <div>You are not logged in. You are viewing studies without visit tracking.</div>
        </div> -->
    </div>
</body>

</html>