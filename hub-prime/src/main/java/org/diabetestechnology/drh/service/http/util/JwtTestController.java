package org.diabetestechnology.drh.service.http.util;

import java.util.HashMap;
import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * Controller for testing JWT authentication
 */
@RestController
@RequestMapping("/api/jwt-test")
@Tag(name = "JWT Test API")
public class JwtTestController {

    private static final Logger LOG = LoggerFactory.getLogger(JwtTestController.class);

    /**
     * Test endpoint to check JWT authentication status
     * @param request The HTTP request
     * @return Authentication status and details
     */
    @GetMapping("/status")
    @Operation(summary = "Check JWT authentication status")
    public Map<String, Object> checkJwtStatus(HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();

        // Get authentication from security context
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        // Check if authenticated
        boolean isAuthenticated = auth != null && auth.isAuthenticated()
                && !"anonymousUser".equals(auth.getPrincipal().toString());
        result.put("authenticated", isAuthenticated);

        if (isAuthenticated) {
            result.put("username", auth.getName());
            result.put("authorities", auth.getAuthorities());
        }

        // Check for JWT in request
        String authHeader = request.getHeader("Authorization");
        boolean hasAuthHeader = authHeader != null && authHeader.startsWith("Bearer ");
        result.put("hasAuthorizationHeader", hasAuthHeader);

        // Check for JWT cookie
        Cookie[] cookies = request.getCookies();
        boolean hasJwtCookie = false;
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("jwt".equals(cookie.getName())) {
                    hasJwtCookie = true;
                    break;
                }
            }
        }
        result.put("hasJwtCookie", hasJwtCookie);

        // Log the result for debugging
        LOG.info("JWT Status Check: {}", result);

        return result;
    }
}
