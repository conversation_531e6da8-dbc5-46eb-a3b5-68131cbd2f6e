package org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant;

public class LogDescription {
    // LEVEL 0
    public static final String ERROR = "An error occurred.";

    // LEVEL 1
    public static final String SKIP_LOGIN = "Skip authentication.";
    public static final String ORCID_LOGIN = "ORCID authentication.";
    public static final String GIT_LOGIN = "GitHub authentication.";
    public static final String HOME = "Load home page.";
    public static final String STUDIES = "Access studies menu.";
    public static final String COHORT = "Open cohort page.";
    public static final String ASK_DRH = "Access Ask DRH menu.";
    // public static final String ACTIVITY_LOG = "Activity log";
    public static final String CONSOLE = "Access console menu.";
    public static final String DOCUMENTATION = "Access documentation menu.";
    public static final String PROFILE = "View profile page.";
    public static final String PROFILE_EMAIL = "View profile email verification page.";
    public static final String VERIFY_OTP = "Verify OTP for email verification.";
    public static final String CREATE_USER_PROFILE = "Create user profile.";
    public static final String SAVE_USER_PROFILE = "Save practitioner profile.";

    // LEVEL 2
    // STUDIES
    public static final String DASHBOARD = "View dashboard.";
    public static final String ALL_STUDIES = "View all studies.";
    public static final String POPULATION_PERCENTAGE = "View population percentage.";
    public static final String MY_STUDIES = "View my studies.";
    // ASK_DRH
    public static final String ASK_DRH_DATA = "Load Ask DRH data.";
    public static final String ASK_DRH_RESEARCH_JOURNAL = "Load Ask DRH research journal.";
    public static final String ASK_DRH_ICODE = "Load Ask DRH iCODE.";
    // CONSOLE
    public static final String CONSOLE_PROJECT = "View console project.";
    public static final String CONSOLE_HEALTH_INFORMATION = "View health information.";
    public static final String CONSOLE_SCHEMA = "View schema.";
    // DOCUMENTATION
    public static final String DOCUMENTATION_OPEN_API = "Access DRH OpenAPI interface.";
    public static final String DOCUMENTATION_ANNOUNCEMENTS = "View announcements.";

    // LEVEL 3
    public static final String STUDIES_INDIVIDUAL = "View study details.";
    public static final String STUDIES_PARTICIPANT = "View participant details.";
    public static final String ALL_STUDIES_CGM = "Access CGM files for all studies.";
    public static final String STUDIES_CGM = "Access CGM files for a specific study.";

    public static final String SUCCESSFUL_STUDY_INTERACTION = "Successful interaction with study.";
    public static final String FAILED_STUDY_INTERACTION = "Failed interaction with study.";
    public static final String SUCCESSFUL_PARTICIPANT_INTERACTION = "Successful interaction with participant.";
    public static final String FAILED_PARTICIPANT_INTERACTION = "Failed interaction with participant.";
    public static final String DATABASE_INTERACTION = "Database interaction.";
    public static final String SUCCESSFUL_PARTICIPANT_FILE_INTERACTION = "Successful interaction with participant files.";
    public static final String FAILED_PARTICIPANT_FILE_INTERACTION = "Failed interaction with participant files.";
    public static final String SUCCESSFUL_CGM_FILE_INTERACTION = "Successful interaction with CGM files.";
    public static final String FAILED_CGM_FILE_INTERACTION = "Failed interaction with CGM files.";
    public static final String SUCCESSFUL_MEALS_OR_FITNESS_INTERACTION = "Successful interaction with meals or fitness data.";
    public static final String FAILED_MEALS_OR_FITNESS_INTERACTION = "Failed interaction with meals or fitness data.";
    public static final String USER_SESSION = "User session management.";

    public static final String CREATE_STUDY = "Create a new study.";

    // LEVEL 4
    public static final String AI_ASK_DRH_DATA = "Access and analyze data using Vanna.ai.";
    public static final String AI_ASK_DRH_RESEARCH_JOURNAL = "Explore research journals and insights using Anything-LLM.";
    public static final String AI_ASK_DRH_ICODE = "Access iCODE-specific features and insights using Anything-LLM.";

    public static final String UPLOAD_STUDY_DATABASE = "Upload study database.";
    public static final String STUDY_SETTINGS = "Access study settings and configurations.";
    public static final String COLLABORATION_AND_TEAMS = "Manage collaboration and team settings.";
    public static final String PUBLICATION_SETTINGS = "Manage publication settings and configurations.";
    public static final String ADD_PARTICIPANT = "Add new participant to the study.";
    public static final String EDIT_PARTICIPANT = "Edit participant settings and configurations.";
    public static final String PARTICIPANT_CGM_DATA = "Access participant CGM data.";
    public static final String PARTICIPANT_MEALS_DATA = "Access participant meals data.";
    public static final String PARTICIPANT_FITNESS_DATA = "Access participant fitness data.";

    // LEVEL 5
    public static final String LOGIN = "User authentication.";
    public static final String ACTIVITY_LOG = "Access activity log.";

    // LEVEL 6
    public static final String SAVE_ACTIVITY_LOG = "Save activity log.";

    // LEVEL 7

    // LEVEL 8
    public static final String SAVE_STUDY = "Save a new study.";
    public static final String EDIT_STUDY_SETTINGS = "Edit study settings.";
    public static final String STUDY_DATA_INLINE_EDIT = "Inline edit study data.";
    public static final String CHANGE_STUDY_ARCHIVE_STATUS = "Change study archive status.";
    public static final String CHANGE_PUBLICATION_AUTHOR = "Change publication author.";
    public static final String CHANGE_STUDY_VISIBILITY = "Change study visibility.";
    public static final String CHANGE_STUDY_CITATIONS = "Change study citations.";
    public static final String CHANGE_USER_ROLE = "Change user role.";
    public static final String SAVE_PARTICIPANT = "Save participant details.";
    public static final String UPLOAD_PARTICIPANT_FILE = "Upload participant file.";
    public static final String CHANGE_PARTICIPANT_DETAILS = "Change participant details.";
    public static final String SAVE_CGM_DATA = "Save CGM data.";
    public static final String SAVE_MEALS_FITNESS_DATA = "Save meals or fitness data.";
    public static final String SAVE_COLLABORATION_TEAM = "Save collaboration team details.";
    public static final String DELETE_RESEARCH_STUDY = "Delete research study.";

    public static final String SAVE_PROFILE = "Save profile details.";

}
