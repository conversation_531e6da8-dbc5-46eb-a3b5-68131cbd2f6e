package org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant;

public class LogType {
    // LEVEL 0
    public static final String ERROR = "Error";

    // LEVEL 1
    public static final String SKIP_LOGIN = "Skip Login";
    public static final String ORCID_LOGIN = "Orcid Login";
    public static final String GIT_LOGIN = "GitHub Login";
    public static final String HOME = "Home";
    public static final String STUDIES = "Studies";
    public static final String COHORT = "Cohort";
    public static final String ASK_DRH = "Ask DRH";
    // public static final String ACTIVITY_LOG = "Activity Log";
    public static final String CONSOLE = "Console";
    public static final String DOCUMENTATION = "Documentation";
    public static final String PROFILE = "Profile";
    public static final String PROFILE_EMAIL = "Profile Email";
    public static final String VERIFY_OTP = "Verify OTP";
    public static final String CREATE_USER_PROFILE = "Create User Profile";

    // LEVEL 2
    // STUDIES
    public static final String DASHBOARD = "Dashboard";
    public static final String ALL_STUDIES = "All Studies";
    public static final String POPULATION_PERCENTAGE = "Population Percentage";
    public static final String MY_STUDIES = "My Studies";
    // ADK_DRH
    public static final String ASK_DRH_DATA = "Ask DRH Data";
    public static final String ASK_DRH_RESEARCH_JOURNAL = "Ask DRH Research Journal";
    public static final String ASK_DRH_ICODE = "Ask DRH iCODE";
    // CONSOLE
    public static final String CONSOLE_PROJECT = "Project";
    public static final String CONSOLE_HEALTH_INFORMATION = "Health Information";
    public static final String CONSOLE_SCHEMA = "Schema";
    // DOCUMENTATION
    public static final String DOCUMENTATION_OPEN_API = "DRH Open API UI";
    public static final String DOCUMENTATION_ANNOUNCEMENTS = "Announcements";

    // LEVEL 3
    public static final String STUDIES_INDIVIDUAL = "Study Details";
    public static final String STUDIES_PARTICIPANT = "Participant Dashboard";
    public static final String ALL_STUDIES_CGM = "All CGM Files";
    public static final String STUDIES_CGM = "Study CGM Files";

    public static final String SUCCESSFUL_STUDY_INTERACTION = "Successful Study Interaction";
    public static final String FAILED_STUDY_INTERACTION = "Failed Study Interaction";
    public static final String SUCCESSFUL_PARTICIPANT_INTERACTION = "Successful Participant Interaction";
    public static final String FAILED_PARTICIPANT_INTERACTION = "Failed Participant Interaction";
    public static final String DATABASE_INTERACTION = "Database Interaction";
    public static final String SUCCESSFUL_PARTICIPANT_FILE_INTERACTION = "Successful Participant File Interaction";
    public static final String FAILED_PARTICIPANT_FILE_INTERACTION = "Failed Participant File Interaction";
    public static final String SUCCESSFUL_CGM_FILE_INTERACTION = "Successful CGM File Interaction";
    public static final String FAILED_CGM_FILE_INTERACTION = "Failed CGM File Interaction";
    public static final String SUCCESSFUL_MEALS_OR_FITNESS_INTERACTION = "Successful Meals Or Fitness Interaction";
    public static final String FAILED_MEALS_OR_FITNESS_INTERACTION = "Failed Meals Or Fitness Interaction";
    public static final String USER_SESSION = "User Session";

    public static final String CREATE_STUDY = "Create Study";

    // LEVEL 4
    public static final String AI_ASK_DRH_DATA = "Ask DRH Data - Vanna.ai";
    public static final String AI_ASK_DRH_RESEARCH_JOURNAL = "Ask DRH Research Jornal - Anything-LLM Interaction.";
    public static final String AI_ASK_DRH_ICODE = "Ask DRH iCODE - Anything-LLM Interaction.";

    public static final String UPLOAD_STUDY_DATABASE = "Upload Study Database";
    public static final String STUDY_SETTINGS = "Study Settings";
    public static final String COLLABORATION_AND_TEAMS = "Collaboration and Teams";
    public static final String PUBLICATION_SETTINGS = "Publication Settings";
    public static final String ADD_PARTICIPANT = "Add New Participant";
    public static final String EDIT_PARTICIPANT = "Edit Participant";
    public static final String PARTICIPANT_CGM_DATA = "CGM Data";
    public static final String PARTICIPANT_MEALS_DATA = "Meals Data";
    public static final String PARTICIPANT_FITNESS_DATA = "Fitness Data";

    // LEVEL 5
    public static final String LOGIN = "Login";
    public static final String ACTIVITY_LOG = "Activity Log";

    // LEVEL 6
    public static final String SAVE_ACTIVITY_LOG = "Activity Log - Save";

    // LEVEL 7

    // LEVEL 8
    public static final String SAVE_STUDY = "Save Study";
    public static final String EDIT_STUDY_SETTINGS = "Edit Study Settings";
    public static final String STUDY_DATA_INLINE_EDIT = "Study Data Inline Edit";
    public static final String CHANGE_STUDY_ARCHIVE_STATUS = "Change Study Archive Status";
    public static final String CHANGE_PUBLICATION_AUTHOR = "Change Publication Author";
    public static final String CHANGE_STUDY_VISIBILITY = "Change Study Visibility";
    public static final String CHANGE_STUDY_CITATIONS = "Change Study Citations";
    public static final String CHANGE_USER_ROLE = "Change User Role";
    public static final String SAVE_PARTICIPANT = "Save Participant";
    public static final String CHANGE_PARTICIPANT_DETAILS = "Change Participant Details";
    public static final String SAVE_PROFILE = "Save Profile";
    public static final String SAVE_CGM_DATA = "Save CGM Data";
    public static final String SAVE_MEALS_FITNESS_DATA = "Save Meals or Fitness Data";
    public static final String UPLOAD_PARTICIPANT_FILE = "Upload Participant File";
    public static final String SAVE_COLLABORATION_TEAM = "Save Collaboration Team";
    public static final String DELETE_RESEARCH_STUDY = "Delete Research Study";

    public static final String SAVE_USER_PROFILE = "Save Practitioner Profile";
}
