package org.diabetestechnology.drh.service.http.pg.ux;

import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.request.UserRoleRequest;
import org.diabetestechnology.drh.service.http.pg.service.UserRoleService;
import org.diabetestechnology.drh.service.http.util.JsonUtils;
import org.jooq.JSONB;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
@Tag(name = "DRH User Role Endpoints")
public class UserRoleController {
    private static final Logger LOG = LoggerFactory.getLogger(UserRoleController.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private final UserRoleService userRoleService;
    private final UserNameService userNameService;

    public UserRoleController(UserRoleService userRoleService, UserNameService userNameService) {
        this.userRoleService = userRoleService;
        this.userNameService = userNameService;
    }

    @GetMapping("/user-roles")
    @Operation(summary = "Retrieve all user roles")
    @ResponseBody
    public Response getUserRoles() {
        LOG.info("Fetching list of user roles...");
        try {
            final Boolean isSuperAdmin = userNameService.isSuperAdmin();
            final Boolean isAdmin = userNameService.isAdmin();
            if (!isSuperAdmin && !isAdmin) {
                LOG.warn("Unauthorized access attempt to user list by non-super admin user");
                return Response.builder()
                        .status("error")
                        .message("Unauthorized access to user list")
                        .errors(Map.of("error", "You do not have permission to access this resource."))
                        .build();
            }
            JSONB userRoles = userRoleService.getUserRoles();
            LOG.info("Successfully retrieved user roles");
            return Response.builder()
                    .data(Map.of("UserRoles",
                            JsonUtils.jsonStringToMapOrList(userRoles.data())))
                    .status("success")
                    .message("User roles fetched successfully")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error fetching user roles", e);
            Response errorResponse = Response.builder()
                    .status("error")
                    .message("Failed to fetch user roles")
                    .errors(Map.of("exception", e.getMessage()))
                    .build();
            return errorResponse;
        }
    }

    @GetMapping("/user-list")
    @Operation(summary = "Retrieve list of users with roles and metadata")
    @ResponseBody
    public Response getUserList() {
        LOG.info("Fetching list of users...");
        try {
            final Boolean isSuperAdmin = userNameService.isSuperAdmin();
            final Boolean isAdmin = userNameService.isAdmin();
            if (!isSuperAdmin && !isAdmin) {
                LOG.warn("Unauthorized access attempt to user list by non-super admin user");
                return Response.builder()
                        .status("error")
                        .message("Unauthorized access to user list")
                        .errors(Map.of("error", "You do not have permission to access this resource."))
                        .build();
            }
            JSONB users = userRoleService.getUserList();
            LOG.info("Successfully retrieved users list");
            return Response.builder()
                    .data(Map.of("Users", JsonUtils.jsonStringToMapOrList(users.data())))
                    .status("success")
                    .message("User list fetched successfully")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error fetching user list", e);
            Response errorResponse = Response.builder()
                    .status("error")
                    .message("Failed to fetch user list")
                    .errors(Map.of("exception", e.getMessage()))
                    .build();
            return errorResponse;
        }
    }

    @PutMapping("/user-role")
    @Operation(summary = "Update user role")
    // @RequiresPermission(resource = "ADMINISTRATION", permissions = {
    // @Permission(permissionName = "Change User Roles", action = "VIEW/EDIT")
    // })
    @ResponseBody
    public Response updateUserRole(@RequestBody UserRoleRequest request) {
        LOG.info("Updating user roles...");
        try {
            Object response = userRoleService.updateUserRole(request);
            JsonNode responseJson = OBJECT_MAPPER.readTree(response.toString());
            if (responseJson.has("status") && "failure".equals(responseJson.get("status").asText())) {
                String errorMessage = responseJson.has("message") ? responseJson.get("message").asText()
                        : "Unknown error occurred.";
                JsonNode errorDetails = responseJson.has("error_details") ? responseJson.get("error_details") : null;
                LOG.error("Error updating user roles : " + errorMessage);
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message(errorMessage)
                        .errors(errorDetails != null ? errorDetails.toString() : null)
                        .build();
            }
            LOG.info("Successfully updated user roles");
            return Response.builder()
                    .data(Map.of("response",
                            response))
                    .status("success")
                    .message("User roles updated successfully")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error updating user role", e);
            Response errorResponse = Response.builder()
                    .status("error")
                    .message("Failed to update user roles")
                    .errors(Map.of("exception", e.getMessage()))
                    .build();
            return errorResponse;
        }
    }

    @GetMapping("/roles-permissions/{userId}")
    @Operation(summary = "Get roles and permissions for a user")
    @ResponseBody
    public Response getUserRolesAndPermissions(@RequestParam String userId) {
        LOG.info("Fetching roles and permissions for userId: {}", userId);
        try {
            JSONB response = userRoleService.getUserRolesAndPermissions(userId);
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(response.toString());

            // Check for status key and log if it's "error"
            if (jsonNode.has("status") && "error".equalsIgnoreCase(jsonNode.get("status").asText())) {
                LOG.error("Error fetching roles and permissions: " + jsonNode.get("message").asText());
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message("Failed to fetch user roles and permissions")
                        .errors("Failed to fetch user roles and permissions")
                        .build();
            }
            JsonNode responseJson = OBJECT_MAPPER.readTree(response.toString());
            if (responseJson.has("status") && "failure".equals(responseJson.get("status").asText())) {
                String errorMessage = responseJson.has("message") ? responseJson.get("message").asText()
                        : "Unknown error occurred.";
                JsonNode errorDetails = responseJson.has("error_details") ? responseJson.get("error_details") : null;
                LOG.error("Error fetching roles and permissions: " + errorMessage);
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message(errorMessage)
                        .errors(errorDetails != null ? errorDetails.toString() : null)
                        .build();
            }
            LOG.info("Successfully retrieved roles and permissions");
            return Response.builder()
                    .data(Map.of("response",
                            JsonUtils.jsonStringToMapOrList(response.data())))
                    .status("success")
                    .message("User roles and permissions fetched successfully")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error fetching user roles and permissions", e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to fetch user roles and permissions")
                    .errors(Map.of("exception", e.getMessage()))
                    .build();
        }
    }

    @GetMapping("/permissions/by-roles")
    @Operation(summary = "Get distinct permissions for given role names")
    @ResponseBody
    public Response getPermissionsByRoles(@RequestParam(required = false) String userPartyId) {

        LOG.info("Fetching distinct permissions for roles");
        try {
            final var response = userRoleService.getPermissionsByRoles(userPartyId);
            JsonNode responseJson = OBJECT_MAPPER.readTree(response.toString());
            if (responseJson.has("status") && "failure".equals(responseJson.get("status").asText())) {
                String errorMessage = responseJson.has("message") ? responseJson.get("message").asText()
                        : "Unknown error occurred.";
                JsonNode errorDetails = responseJson.has("error_details") ? responseJson.get("error_details") : null;
                LOG.error("Error fetching roles and permissions: " + errorMessage);
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message(errorMessage)
                        .errors(errorDetails != null ? errorDetails.toString() : null)
                        .build();
            }
            return Response.builder()
                    .data(Map.of("response", JsonUtils.jsonStringToMapOrList(response)))
                    .status("success")
                    .message("Permissions fetched successfully for given roles")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error fetching permissions for roles", e);
            return Response.builder()
                    .status("error")
                    .message("Failed to fetch permissions")
                    .errors(Map.of("exception", e.getMessage()))
                    .build();
        }
    }

    @GetMapping("/permissions/guest-role")
    @Operation(summary = "Get distinct permissions for guest role")
    @ResponseBody
    public Response getPermissionsOfGuestRole() {
        LOG.info("Fetching distinct permissions for guest role");
        try {
            Map<String, Object> permissionsData = userRoleService.getPermissionsOfGuestRole();

            if (permissionsData == null || permissionsData.isEmpty()) {
                LOG.warn("No permissions found for guest role.");
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message("No permissions found for guest role.")
                        .errors(null)
                        .build();
            }
            return Response.builder()
                    .data(Map.of("permissions", permissionsData.get("permissions")))
                    .status("success")
                    .message("Permissions fetched successfully for guest.")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error fetching permissions for guest role", e);
            return Response.builder()
                    .status("error")
                    .message("Failed to fetch guest permissions.")
                    .errors(Map.of("exception", e.getMessage()))
                    .build();
        }
    }

}
