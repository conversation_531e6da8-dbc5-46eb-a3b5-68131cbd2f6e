package org.diabetestechnology.drh.service.http.hub.prime.ux;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;

import org.diabetestechnology.drh.service.http.Helpers;
import org.diabetestechnology.drh.service.http.hub.prime.AppConfig;
import org.diabetestechnology.drh.service.http.hub.prime.LoginLogger;
import org.diabetestechnology.drh.service.http.hub.prime.route.RouteMapping;
import org.diabetestechnology.drh.service.http.hub.prime.service.AuthenticationService;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.ActivityLogService;
import org.diabetestechnology.drh.service.http.pg.constant.UserVerificationStatus;
import org.diabetestechnology.drh.service.http.pg.service.MasterService;
import org.diabetestechnology.drh.service.http.pg.service.PractitionerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import org.springframework.stereotype.Controller;

@Controller
@Tag(name = "DRH Hub UX API")
public class PrimeController {
    @Value("")
    private String chatAiBaseUrl;

    private static final Logger LOG = LoggerFactory.getLogger(PrimeController.class.getName());
    private final AppConfig appConfig;

    private final Presentation presentation;

    private final PractitionerService practitionerService;
    private final ActivityLogService activityLogService;
    private final AuthenticationService authenticateionService;
    private final UserNameService userNameService;
    private final MasterService masterService;

    public PrimeController(AppConfig appConfig, final Presentation presentation,
            PractitionerService practitionerService, ActivityLogService activityLogService,
            AuthenticationService authenticateionService, UserNameService userNameService,
            MasterService masterService) {
        this.appConfig = appConfig;
        this.presentation = presentation;
        this.practitionerService = practitionerService;
        this.activityLogService = activityLogService;
        this.authenticateionService = authenticateionService;
        this.userNameService = userNameService;
        this.masterService = masterService;
    }

    @RouteMapping(label = "Home", siblingOrder = 0)

    @GetMapping("/home")
    public String home(final Model model, final HttpServletRequest request,
            @RequestParam(name = "from", required = false) String from) throws Exception {
        model.addAttribute("chatAiBaseUrl", chatAiBaseUrl);
        LOG.info("Load Home Page");
        model.addAttribute("isAuthenticated", false);

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() && !(authentication
                .getPrincipal() instanceof AnonymousAuthenticationToken)) {

            LOG.info("User is authenticated: {}", authentication.getPrincipal());
            String userName = "Anonymous";
            final var providerId = userNameService.getUserId();
            final var statusId = masterService.getUserVerificationStatusId(UserVerificationStatus.COMPLETED);
            Boolean isUserVerified = practitionerService.isUserVerified(providerId, statusId);
            Boolean isSuperAdmin = presentation.isSuperAdmin();
            model.addAttribute("isUserVerified", isUserVerified);
            Boolean isAdmin = userNameService.isAdmin();
            model.addAttribute("isAdmin", isAdmin);
            // Handle both OAuth2 and database authentication
            if (authentication.getPrincipal() instanceof OAuth2User) {
                LOG.info("Oauth2User User Home");
                OAuth2User oauth2User = (OAuth2User) authentication.getPrincipal();
                model.addAttribute("isAuthenticated", true);
                model.addAttribute("userProvider", oauth2User.getAttribute("provider"));
                userName = oauth2User.getAttribute("name") != null ? oauth2User.getAttribute("name")
                        : oauth2User
                                .getAttribute("login");
                model.addAttribute("userName", userName);
                model.addAttribute("isSuperAdmin", false);

            } else if (authentication.getPrincipal() instanceof UserDetails) {
                LOG.info("DRH User Home");
                UserDetails userDetails = (UserDetails) authentication.getPrincipal();
                model.addAttribute("isAuthenticated", true);
                model.addAttribute("userProvider", "Database");
                model.addAttribute("isSuperAdmin", presentation.isSuperAdmin());
                userName = userDetails.getUsername();
                model.addAttribute("userName", userName);
            } else {
                LoginLogger.anonymousLogin(userName);
            }

            Boolean existingUser = practitionerService.isUserExists();
            model.addAttribute("isProfileCompleted", existingUser);
            model.addAttribute("Organization", practitionerService.getUserOrganization());
            model.addAttribute("isSuperAdmin", presentation.isSuperAdmin());
            if (existingUser) {
                final var name = presentation.getUsername();
                model.addAttribute("userName", name);
                LoginLogger.practitionerLogin(userName);
            } else if (userName != null && userName.equalsIgnoreCase("Anonymous")) {
                LoginLogger.anonymousLogin(userName);
                if ("skip-login".equals(from)) {
                    activityLogService.saveLoginActivity("Anonymous");
                }
            } else if (isSuperAdmin) {
                return presentation.populateModel("page/home", model, request);
            } else {
                return "redirect:/profile/email/info";
            }

        } else {
            LOG.info("Guest User Home");
            model.addAttribute("isAuthenticated", false);
            model.addAttribute("isProfileCompleted", false);
            model.addAttribute("isSuperAdmin", false);
            model.addAttribute("isUserVerified", false);
            LoginLogger.anonymousLogin("Anonymous");
        }

        return presentation.populateModel("page/home", model, request);
    }

    @GetMapping("/skip-login")
    public String skipLogin(final Model model, final HttpServletRequest request) {
        return "redirect:/home?from=skip-login";
    }

    @Hidden
    @GetMapping(value = "/metadata", produces = { MediaType.APPLICATION_XML_VALUE })
    @Operation(summary = "DRH server's conformance statement")
    public String metadata(final Model model, HttpServletRequest request) {
        final var baseUrl = Helpers.getBaseUrl(request);
        LOG.info("Read Meta Data");
        model.addAttribute("version", appConfig.getVersion());
        model.addAttribute("implUrlValue", baseUrl);
        return "metadata.xml";
    }

    @GetMapping("/")
    public String login(HttpServletRequest request) {
        if (authenticateionService.isUserAuthenticated(request)) {

            return "redirect:/home"; // Redirect to the home page if user is already logged in
        }
        return "login/login";
    }

}
