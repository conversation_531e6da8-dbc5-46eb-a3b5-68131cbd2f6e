package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Map;

import org.jooq.Field;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.request.CollaborationTeamRequest;
import org.diabetestechnology.drh.service.http.pg.request.InvestigatorRequest;

import com.fasterxml.jackson.databind.ObjectWriter;

import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.ResultQuery;
import org.jooq.Condition;
import org.jooq.SelectJoinStep;
import org.jooq.SelectSelectStep;
import org.jooq.SelectConditionStep;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

@ExtendWith(MockitoExtension.class)

public class InvestigatorServiceTest {
    @Mock
    private DSLContext dsl;

    @Mock
    private ResultQuery<Record1<String>> queryMock;
    @Mock
    private InteractionService interactionService;

    @InjectMocks
    private InvestigatorService investigatorService;

    @Mock
    private ObjectMapper mockObjectMapper;
    @Mock
    private DbActivityService activityLogService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetInvestigator_ReturnsFormattedJson() throws Exception {

        String jsonResponse = "[{\"investigator_name\": \"Dr. John Doe\"}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        DSLContext dsl = mock(DSLContext.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        java.lang.reflect.Field dslField = InvestigatorService.class.getDeclaredField("dsl");
        dslField.setAccessible(true);
        dslField.set(investigatorService, dsl);

        Object result = investigatorService.getInvestigator();

        JSONB expectedResult = JSONB.valueOf(jsonResponse);
        JSONB actualResult = (JSONB) result;
        assertEquals(expectedResult, actualResult);

        assertEquals(expectedResult, actualResult);

    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetInvestigator_ReturnsEmptyJson_WhenJsonProcessingFails() throws Exception {

        JSONB mockResult = mock(JSONB.class);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        DSLContext dslMock = mock(DSLContext.class);
        when(dslMock.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        java.lang.reflect.Field dslField = InvestigatorService.class.getDeclaredField("dsl");
        dslField.setAccessible(true);
        dslField.set(investigatorService, dslMock);

        Object actualResult = investigatorService.getInvestigator();

        assertEquals(mockResult, actualResult);

    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveInvestigator_ReturnsJsonResult() throws Exception {

        InvestigatorRequest mockRequest = new InvestigatorRequest(
                "study123",
                new String[] { "practitioner1", "practitioner2" },
                InvestigatorRequest.CollabTeamType.investigator);

        String jsonResponse = "{\"status\":\"success\",\"message\":\"Investigator saved\"}";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        DSLContext dsl = mock(DSLContext.class);
        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        java.lang.reflect.Field dslField = InvestigatorService.class.getDeclaredField("dsl");
        dslField.setAccessible(true);
        dslField.set(investigatorService, dsl);

        Object actualResult = investigatorService.saveInvestigator(mockRequest);

        assertEquals(mockResult, actualResult);

        verify(dsl, times(2)).select(any(Field.class));
        verify(selectMock, times(1)).fetchOneInto(JSONB.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveInvestigator_ReturnsNull_WhenQueryFails() throws Exception {

        // Arrange
        InvestigatorRequest mockRequest = new InvestigatorRequest(
                "study123",
                new String[] { "practitioner1", "practitioner2" },
                InvestigatorRequest.CollabTeamType.author);

        DSLContext dsl = mock(DSLContext.class);
        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(null);
        java.lang.reflect.Field dslField = InvestigatorService.class.getDeclaredField("dsl");
        dslField.setAccessible(true);
        dslField.set(investigatorService, dsl);

        Object actualResult = investigatorService.saveInvestigator(mockRequest);

        assertNull(actualResult);
        verify(dsl, times(2)).select(any(Field.class));
        verify(selectMock, times(1)).fetchOneInto(JSONB.class);
    }

    @Test
    void testSaveCollaborationTeam_Successss() throws Exception {
        InvestigatorRequest mockRequest = new InvestigatorRequest(
                "study123",
                new String[] { "practitioner1", "practitioner2" },
                InvestigatorRequest.CollabTeamType.investigator);

        String mockResultJson = "{\"status\":\"success\",\"message\":\"investigator created successfully\"}";
        JSONB mockJsonbResult = JSONB.valueOf(mockResultJson);

        InvestigatorService spyService = Mockito.spy(investigatorService);
        doReturn(mockJsonbResult).when(spyService).saveInvestigator(mockRequest);

        lenient().doNothing().when(interactionService).saveStudyInteraction(
                anyString(), anyString(), anyString(), anyString(),
                anyString(), anyString(), anyString(), anyString(),
                anyString(), anyInt(), anyString(), anyString(),
                anyString());

        lenient().when(interactionService.getHubIntercationIdOfStudy(anyString())).thenReturn("mock-hub-id");

        Response response = spyService.saveCollaborationTeam(mockRequest);

        assertNotNull(response);
        assertEquals("success", response.getStatus());
        assertEquals("investigator created successfully", response.getMessage());

        ObjectMapper mapper = new ObjectMapper();
        String rawJson = (String) response.getData().get("InvestigatorDetails");
        JsonNode root = mapper.readTree(rawJson);
        Map<String, Object> actualDetails = mapper.convertValue(root,
                new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {
                });

        Map<String, Object> expectedDetails = Map.of(
                "status", "success",
                "message", "investigator created successfully");

        assertEquals(expectedDetails, actualDetails);
    }

    @Test
    void testSaveCollaborationTeam_Exception() {
        InvestigatorRequest mockRequest = new InvestigatorRequest(
                "study123",
                new String[] { "practitioner1", "practitioner2" },
                InvestigatorRequest.CollabTeamType.investigator);

        Response response = investigatorService.saveCollaborationTeam(mockRequest);

        assertNotNull(response);
        System.out.println("Response message: " + response.getMessage());

        assertEquals("error", response.getStatus());
        assertTrue(response.getMessage().toLowerCase().contains("failed")); // Case-insensitive
        assertTrue(response.getMessage().toLowerCase().contains("investigator"));
    }

    @Test
    void testPrapareCollabRequest_SuccessWithAllFields() {
        CollaborationTeamRequest request = new CollaborationTeamRequest(
                "study123",
                new String[] { "coInvestigator1", "coInvestigator2" },
                "principalInvestigator1",
                "nominatedPrincipalInvestigator1",
                new String[] { "coAuthor1", "coAuthor2" },
                new String[] { "studyTeam1", "studyTeam2" });

        Response response = investigatorService.prapareCollabRequest(request);

        assertNotNull(response);
        assertEquals("success", response.getStatus());
        assertEquals(" Successfully added practioners to collaboration team.",
                response.getMessage());
    }

    @Test
    void testPrapareCollabRequest_SuccessWithMinimalFields() {
        CollaborationTeamRequest request = new CollaborationTeamRequest(
                "study123",
                new String[] { "coInvestigator1", "coInvestigator2" },
                "principalInvestigator1",
                "nominatedPrincipalInvestigator1",
                new String[] { "coAuthor1", "coAuthor2" },
                new String[] { "studyTeam1", "studyTeam2" });

        Response response = investigatorService.prapareCollabRequest(request);

        assertNotNull(response);
        assertEquals("success", response.getStatus());
    }

    @Test
    void testPrapareCollabRequest_HandlesNullRequest() {
        NullPointerException exception = assertThrows(NullPointerException.class, () -> {
            investigatorService.prapareCollabRequest(null);
        });

        assertTrue(exception.getMessage() == null || exception.getMessage().contains("studyId"));

    }

    @Test
    void testPrapareCollabRequest_FailureOnException() {
        InvestigatorService spyService = spy(investigatorService);
        doThrow(new RuntimeException("Unexpected error")).when(spyService).saveCollaborationTeam(any());

        CollaborationTeamRequest request = new CollaborationTeamRequest(
                "study123",
                new String[] { "coInvestigator1", "coInvestigator2" },
                "principalInvestigator1",
                "nominatedPrincipalInvestigator1",
                new String[] { "coAuthor1", "coAuthor2" },
                new String[] { "studyTeam1", "studyTeam2" });
        Response response = spyService.prapareCollabRequest(request);

        assertNotNull(response);
        assertEquals("error", response.getStatus());
        assertTrue(response.getMessage().contains("failed to create an collaboration team"));
    }

}