package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.Optional;

import org.diabetestechnology.drh.service.http.GitHubUserAuthorizationFilter;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.AuditService;
import org.diabetestechnology.drh.service.http.pg.request.PractitionerRequest;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;

import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectLimitPercentStep;
import org.jooq.SelectSelectStep;
import org.jooq.impl.DSL;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class PractitionerServiceTest {

    @Mock
    private DSLContext dsl;

    @Mock
    private UserNameService userNameService;
    @Mock
    private DbActivityService activityLogService;
    @Mock
    private AuditService auditService;
    @Mock
    private AuthUserDetailsService authUserDetailsService;
    @Mock
    private GitHubUserAuthorizationFilter gitHubUserAuthorizationFilter;

    @InjectMocks
    private PractitionerService practitionerService;

    private SelectSelectStep<Record1<JSONB>> selectMock;
    private SelectJoinStep<Record1<JSONB>> joinStepMock;
    private SelectConditionStep<Record1<JSONB>> conditionStepMock;

    @SuppressWarnings("unchecked")
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this); // Initialize mocks

        // Initialize common mocks
        selectMock = mock(SelectSelectStep.class);
        joinStepMock = mock(SelectJoinStep.class);
        conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);

    }

    @SuppressWarnings("unchecked")
    @Test
    void testCreatePractitionerProfile_GitHub() throws Exception {
        PractitionerRequest request = mock(PractitionerRequest.class);
        when(request.name()).thenReturn("John Doe");
        when(request.email()).thenReturn(new String[] { "<EMAIL>" });
        when(request.organizationPartyId()).thenReturn("Org123");

        when(userNameService.getUserId()).thenReturn("user123");
        when(userNameService.getUserProvider()).thenReturn("GitHub");
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(JSONB.valueOf("{\"status\": \"success\"}"));

        JSONB result = practitionerService.createPractitionerProfile(request);

        assertNotNull(result);
        assertEquals("{\"status\": \"success\"}", result.data());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testCreatePractitionerProfile_Orcid() throws Exception {
        PractitionerRequest request = mock(PractitionerRequest.class);
        when(request.name()).thenReturn("Jane Doe");
        when(request.email()).thenReturn(new String[] { "<EMAIL>" });
        when(request.organizationPartyId()).thenReturn("Org456");
        when(request.orcid()).thenReturn("0000-0002-1825-0097");

        when(userNameService.getUserId()).thenReturn("user456");
        when(userNameService.getUserProvider()).thenReturn("Orcid");
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(JSONB.valueOf("{\"status\": \"success\"}"));

        JSONB result = practitionerService.createPractitionerProfile(request);

        assertNotNull(result);
        assertEquals("{\"status\": \"success\"}", result.data());
    }

    @Test
    void testCreatePractitionerProfile_InvalidProvider() throws Exception {
        PractitionerRequest request = mock(PractitionerRequest.class);
        when(userNameService.getUserProvider()).thenReturn("Unknown");

        JSONB result = practitionerService.createPractitionerProfile(request);

        assertNull(result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetUserDetails_UserFound() {
        String userId = "testUser123";
        JSONB jsonbMock = JSONB.valueOf("{\"practitioner_id\": 123}");

        when(userNameService.getUserId()).thenReturn(userId);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(jsonbMock);

        Object result = practitionerService.getUserDetails();

        assertEquals(jsonbMock.data(), result);
    }

    @Test
    void testGetUserDetails_AnonymousUser() {
        when(userNameService.getUserId()).thenReturn("Anonymous");

        Object result = practitionerService.getUserDetails();

        assertEquals("{}", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetUserDetails_NoUserFound() {
        when(userNameService.getUserId()).thenReturn("unknownUser");
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(null);

        Object result = practitionerService.getUserDetails();

        assertEquals("{}", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetUserDetails_ExceptionHandling() {
        when(userNameService.getUserId()).thenReturn("validUserId");
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("DB error"));

        Object result = practitionerService.getUserDetails();

        assertEquals("{}", result);
    }

    @Test
    void testIsUserExists_Anonymous() {

        when(userNameService.getUserId()).thenReturn("Anonymous");
        Boolean result = practitionerService.isUserExists();
        assertEquals(false, result);
    }

    @Test
    void testIsUserExists_IsEmpty() {

        when(userNameService.getUserId()).thenReturn("");
        Boolean result = practitionerService.isUserExists();
        assertEquals(false, result);
    }

    @Test
    void testIsUserExists_null() {

        when(userNameService.getUserId()).thenReturn(null);
        Boolean result = practitionerService.isUserExists();
        assertEquals(false, result);
    }

    @Test
    void testGetUserOrganization_EmptyUserId() {

        when(userNameService.getUserId()).thenReturn("");
        String result = practitionerService.getUserOrganization();
        assertEquals("", result);
    }

    @Test
    void testGetUserOrganization_AnonymousUser() {

        when(userNameService.getUserId()).thenReturn("Anonymous");
        String result = practitionerService.getUserOrganization();
        assertEquals("", result);
    }

    @Test
    void testGetUserOrganization_NullUserId() {

        when(userNameService.getUserId()).thenReturn(null);
        String result = practitionerService.getUserOrganization();
        assertEquals("", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetUserOrganization_ExceptionThrown() {
        when(userNameService.getUserId()).thenReturn("validUserId");
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        String result = practitionerService.getUserOrganization();
        assertEquals("", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testIsUserExists_ValidUser() {
        when(userNameService.getUserId()).thenReturn("valid-user-id");

        SelectSelectStep<Record1<Boolean>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Boolean>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Boolean>> whereStepMock = mock(SelectConditionStep.class);
        SelectLimitPercentStep<Record1<Boolean>> limitStepMock = mock(SelectLimitPercentStep.class);

        when(dsl.select(DSL.value(true))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.user_profile_view")).thenReturn(fromStepMock);

        when(fromStepMock.where(any(Condition.class))).thenReturn(whereStepMock);
        when(whereStepMock.limit(1)).thenReturn(limitStepMock);
        when(limitStepMock.fetchOptional()).thenReturn(Optional.of(mock(Record1.class)));

        Boolean result = practitionerService.isUserExists();
        assertTrue(result);
    }

    @Test
    void testIsUserExists_ExceptionHandling() {

        when(userNameService.getUserId()).thenReturn("valid-user-id");
        when(dsl.select(DSL.value(true))).thenThrow(new RuntimeException("Database error"));

        Boolean result = practitionerService.isUserExists();
        assertNotNull(result);
        assertFalse(result);
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    @Test
    void test_GetUserOrganization() {
        when(userNameService.getUserId()).thenReturn("valid-user-id");

        SelectSelectStep<Record1<?>> selectMock1 = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<?>> fromStepMock1 = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<?>> whereStepMock1 = mock(SelectConditionStep.class);
        SelectLimitPercentStep<Record1<?>> limitStepMock1 = mock(SelectLimitPercentStep.class);

        // Second Query (Fetching `organization_name`)
        SelectSelectStep<Record1<?>> selectMock2 = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<?>> fromStepMock2 = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<?>> whereStepMock2 = mock(SelectConditionStep.class);
        SelectLimitPercentStep<Record1<?>> limitStepMock2 = mock(SelectLimitPercentStep.class);

        // Mock first query (user_profile_view)
        when(dsl.select(DSL.field("organization_party_id"))).thenReturn((SelectSelectStep) selectMock1);
        when(selectMock1.from("drh_stateless_authentication.user_profile_view")).thenReturn(fromStepMock1);
        when(fromStepMock1.where(any(Condition.class))).thenReturn(whereStepMock1);
        when(whereStepMock1.limit(1)).thenReturn(limitStepMock1);
        when(limitStepMock1.fetchOneInto(String.class)).thenReturn("mockOrgId");

        // Mock second query (organization_party_view)
        when(dsl.select(DSL.field("organization_name"))).thenReturn((SelectSelectStep) selectMock2);
        when(selectMock2.from("drh_stateless_research_study.organization_party_view")).thenReturn(fromStepMock2);
        when(fromStepMock2.where(any(Condition.class))).thenReturn(whereStepMock2);
        when(whereStepMock2.limit(1)).thenReturn(limitStepMock2);
        when(limitStepMock2.fetchOneInto(String.class)).thenReturn("Mock Organization Name");

        String result = practitionerService.getUserOrganization();

        assertNotNull(result);
        assertEquals("Mock Organization Name", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetLoggedInUserDetails_success() {
        String userId = "user123";
        String expectedJson = "{\"practitioner_id\":\"user123\"}";

        JSONB mockJsonb = JSONB.valueOf(expectedJson);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(auditService.getCurrentRequest()).thenReturn(null);
        when(userNameService.getUserId()).thenReturn(userId);
        when(authUserDetailsService.isSuperAdmin()).thenReturn(false);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinMock);
        when(joinMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        Object result = practitionerService.getLoggedInUserDetails();

        assertEquals(mockJsonb, result);
    }

    @Test
    void testGetLoggedInUserDetails_anonymousUser_returnsEmptyJson() {
        when(auditService.getCurrentRequest()).thenReturn(null);
        when(userNameService.getUserId()).thenReturn("Anonymous");

        Object result = practitionerService.getLoggedInUserDetails();

        assertEquals("{}", result.toString());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetLoggedInUserDetails_whenExceptionThrown_returnsEmptyJson() {
        when(auditService.getCurrentRequest()).thenReturn(null);
        when(userNameService.getUserId()).thenReturn("user123");
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("DB error"));

        Object result = practitionerService.getLoggedInUserDetails();

        assertEquals("{}", result.toString());
    }

}
